import React, { useState } from 'react';
import { View, Text, TextInput, TouchableOpacity, Image, StatusBar, Dimensions } from 'react-native';

const { width, height } = Dimensions.get('window');

export function Login() {
  const [formData, setFormData] = useState({
    username: '',
    rememberMe: false,
  });

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const handleLogin = () => {
    // Handle login logic here
    console.log('Login data:', formData);
  };

  return (
    <View style={{ flex: 1, backgroundColor: 'white' }}>
      <StatusBar barStyle="dark-content" backgroundColor="white" />

      {/* Status Bar Area */}
      <View style={{
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        paddingHorizontal: 24,
        paddingTop: 12,
        paddingBottom: 8,
        height: 44
      }}>
        <Text style={{
          fontSize: 17,
          fontWeight: '600',
          color: '#000'
        }}>9:30</Text>

        <View style={{
          width: 6,
          height: 6,
          borderRadius: 3,
          backgroundColor: '#000'
        }} />

        <View style={{ flexDirection: 'row', alignItems: 'center', gap: 2 }}>
          <View style={{
            width: 17,
            height: 10,
            backgroundColor: '#000'
          }} />
          <View style={{
            width: 15,
            height: 10,
            backgroundColor: '#000'
          }} />
          <View style={{
            width: 24,
            height: 12,
            backgroundColor: '#000'
          }} />
        </View>
      </View>

      {/* Main Container */}
      <View style={{
        flex: 1,
        paddingHorizontal: 24,
        paddingTop: 40
      }}>

        {/* Logo Section */}
        <View style={{ alignItems: 'center', marginBottom: 80 }}>
          <Image
            source={require('../assets/Logo.png')}
            style={{
              width: 200,
              height: 64,
              marginBottom: 50
            }}
            resizeMode="contain"
          />

          <Text style={{
            fontSize: 30,
            fontWeight: '700',
            color: '#1F2937',
            textAlign: 'center',
            marginBottom: 8,
            fontFamily: 'Oxanium-Bold'
          }}>
            Welcome Back
          </Text>

          <Text style={{
            fontSize: 16,
            fontWeight: '400',
            color: '#6B7280',
            textAlign: 'center',
            fontFamily: 'Oxanium-Regular'
          }}>
            Login to access your Estate Link account
          </Text>
        </View>

        {/* Form Section */}
        <View>
          <Text style={{
            fontSize: 16,
            fontWeight: '500',
            color: '#374151',
            marginBottom: 8,
            fontFamily: 'Oxanium-Medium'
          }}>
            User Name / Email / Phone Number
          </Text>

          <TextInput
            style={{
              height: 48,
              borderRadius: 8,
              borderWidth: 1,
              borderColor: '#D1D5DB',
              paddingHorizontal: 16,
              backgroundColor: '#F9FAFB',
              fontSize: 14,
              color: '#6B7280',
              marginBottom: 24,
              fontFamily: 'Oxanium-Regular'
            }}
            placeholder="enter your user name / e-mail / phone number"
            placeholderTextColor="#9CA3AF"
            value={formData.username}
            onChangeText={(value) => handleInputChange('username', value)}
          />

          {/* Remember Me Checkbox */}
          <View style={{
            flexDirection: 'row',
            alignItems: 'center',
            marginBottom: 32
          }}>
            <TouchableOpacity
              style={{
                width: 18,
                height: 18,
                borderWidth: 1.5,
                borderColor: '#14B8A6',
                borderRadius: 3,
                marginRight: 12,
                justifyContent: 'center',
                alignItems: 'center',
                backgroundColor: formData.rememberMe ? '#14B8A6' : 'transparent'
              }}
              onPress={() => setFormData(prev => ({ ...prev, rememberMe: !prev.rememberMe }))}
            >
              {formData.rememberMe && (
                <Text style={{
                  color: 'white',
                  fontSize: 12,
                  fontWeight: 'bold'
                }}>✓</Text>
              )}
            </TouchableOpacity>
            <Text style={{
              fontSize: 16,
              fontWeight: '400',
              color: '#374151',
              fontFamily: 'Oxanium-Regular'
            }}>
              Remember me
            </Text>
          </View>

          {/* Login Button */}
          <TouchableOpacity
            style={{
              width: 360,
              height: 48,
              backgroundColor: 'white',
              borderWidth: 1.5,
              borderColor: '#14B8A6',
              borderRadius: 12,
              justifyContent: 'center',
              alignItems: 'center',
              marginBottom: 20,
              opacity: 1
            }}
            onPress={handleLogin}
          >
            <Text style={{
              fontSize: 16,
              fontWeight: '600',
              color: '#14B8A6',
              fontFamily: 'Oxanium-SemiBold'
            }}>
              Login
            </Text>
          </TouchableOpacity>

          {/* Forgot Password Link */}
          <View style={{ alignItems: 'center', marginBottom: 60 }}>
            <TouchableOpacity>
              <Text style={{
                fontSize: 16,
                fontWeight: '600',
                color: '#14B8A6',
                fontFamily: 'Oxanium-SemiBold'
              }}>
                Forgot Password?
              </Text>
            </TouchableOpacity>
          </View>
        </View>

        {/* Bottom Link */}
        <View style={{
          flex: 1,
          justifyContent: 'flex-end',
          alignItems: 'center',
          paddingBottom: 40
        }}>
          <TouchableOpacity>
            <Text style={{
              fontSize: 16,
              fontWeight: '400',
              color: '#374151',
              textDecorationLine: 'underline',
              fontFamily: 'Oxanium-Regular'
            }}>
              Log into Estate Control
            </Text>
          </TouchableOpacity>
        </View>
      </View>
    </View>
  );
}